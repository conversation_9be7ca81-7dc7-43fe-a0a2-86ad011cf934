# DevOps Learning Notes

This repository contains comprehensive notes and documentation for various DevOps topics, focusing on Linux, Docker, Cloud computing, and related technologies.

## Linux
0. [Basic Commands](linux/00.basic-commands.md)
1. [File Ownership and Permissions Management](linux/01.chown+chmod.md)
2. [Process Management Commands](linux/02.process-management.md)
3. [Text Processing Commands](linux/03.text-processing.md)
4. [Packages Management](linux/04.packages-management.md)
5. [Network and Security](linux/05.network-and-security.md)
6. [Automation and Scripting](linux/06.automation-and-scripting.md)

## Docker

*(Coming soon)*

## Cloud Computing

*(Coming soon)*

## CI/CD

*(Coming soon)*

## Monitoring and Logging

*(Coming soon)*

## Infrastructure as Code (IaC)

*(Coming soon)*

## Container Orchestration

*(Coming soon)*

---

*This repository is actively maintained and updated with new content regularly.*